This is my implementation of the "controlled random search" (CRS2) algorithm
with the "local mutation" modification, as defined by:

       <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>, "Some variants of the controlled random
       search algorithm for global optimization," J. Optim. Theory Appl.
       130 (2), 253-264 (2006).

The original CRS2 algorithm was described by:

	<PERSON><PERSON> <PERSON><PERSON>, "A controlled random search procedure for global
	optimization," in Towards Global Optimization 2, p. 71-84
	edited by <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> (North-Holland Press,
	Amsterdam, 1978).

It is under the same MIT license as the rest of my code in NLopt (see
../COPYRIGHT).

<PERSON>
September 2007
