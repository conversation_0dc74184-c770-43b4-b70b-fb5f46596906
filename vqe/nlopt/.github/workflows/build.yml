name: Build
on: [push, pull_request]
jobs:
  linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build
        run: |
          sudo apt-get update -y && sudo apt-get install python3-dev python3-numpy guile-3.0-dev liboctave-dev cmake gfortran g++-mingw-w64-x86-64 gfortran-mingw-w64-x86-64
          pip install mkdocs python-markdown-math --user
          PATH=$PATH:~/.local/bin mkdocs build
          mkdir build && pushd build
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_MATLAB=OFF -DNLOPT_FORTRAN=ON -DCMAKE_C_FLAGS='-std=c89 -pedantic -D_POSIX_C_SOURCE=200809L -Wall -Wextra' -DCMAKE_CXX_FLAGS='-Wall -Wextra' ..
          make install -j2 && ctest -j2 --output-on-failure
          rm -rf * ~/.local
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_PYTHON=OFF -DNLOPT_OCTAVE=OFF -DNLOPT_GUILE=OFF -DNLOPT_MATLAB=OFF -DNLOPT_FORTRAN=ON -DCMAKE_TOOLCHAIN_FILE=$PWD/../cmake/toolchain-x86_64-w64-mingw32.cmake ..
          make install -j2

  macos:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build
        run: |
          brew upgrade && brew install swig octave
          mkdir build && pushd build
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_FORTRAN=OFF -DNLOPT_GUILE=OFF ..
          make install -j3 && ctest --output-on-failure

  windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build
        shell: cmd
        run: |
          pip install numpy
          cmake -DCMAKE_INSTALL_PREFIX=%cd:\=/%/install .
          cmake --build . --config Release --target install
          xcopy %cd%\install\bin\nlopt.dll %cd%\src\swig
          xcopy %cd%\install\Lib\site-packages\_nlopt*.pyd %cd%\src\swig
          dir /p %cd%\src\swig
          ctest -C Release --output-on-failure --timeout 100
