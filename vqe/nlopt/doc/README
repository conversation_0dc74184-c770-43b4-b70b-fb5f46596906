This is the documentation tree for NLopt.

Markdown (.md) files for NLopt documentation live in $(top_src_dir)/doc/docs.

To build and visualize the HTML documentation locally using the
mkdocs package (useful for verifying on your local machine before
committing):

1. Install the `mkdocs` package, together with the requirements in
requirements.txt:

% pip install mkdocs
% pip install -r requirements.txt

2. Run the following command from the top-level NLopt repository tree:

% mkdocs serve

and then open the following URL in a browser window:

http://127.0.0.1:8000/NLopt

This launches a little web server on your local machine
plus a filesystem hook for rebuilding the documentation
tree automatically whenever any .md file is modified, so
you can see what the actual HTML documentation looks
like in real time as you edit the source.
